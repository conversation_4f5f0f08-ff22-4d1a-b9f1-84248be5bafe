package com.example.contentobserverdb1;


import android.content.ContentResolver;
import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import java.util.Random;

public class MainActivity extends AppCompatActivity implements View.OnClickListener {
    private ContentResolver resolver;
    private Uri uri;
    private ContentValues values;
    private Button btnInsert, btnUpdate, btnDelete, btnSelect;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        initView(); // 初始化界面
        createDB(); // 创建数据库
    }

    private void initView() {
        btnInsert = findViewById(R.id.btn_insert);
        btnUpdate = findViewById(R.id.btn_update);
        btnDelete = findViewById(R.id.btn_delete);
        btnSelect = findViewById(R.id.btn_select);

        btnInsert.setOnClickListener(this);
        btnUpdate.setOnClickListener(this);
        btnDelete.setOnClickListener(this);
        btnSelect.setOnClickListener(this);
    }

    private void createDB() {
        // 创建数据库并添加3条初始数据
        PersonDBOpenHelper helper = new PersonDBOpenHelper(this);
        SQLiteDatabase db = helper.getWritableDatabase();
        for (int i = 0; i < 3; i++) {
            ContentValues values = new ContentValues();
            values.put("name", "itcast" + i);
            db.insert("info", null, values);
        }
        db.close();
    }

    @Override
    public void onClick(View v) {
        resolver = getContentResolver();
        uri = Uri.parse("content://com.example.contentobserverdb/info");
        values = new ContentValues();

        if (v.getId() == R.id.btn_insert) {
            Random random = new Random();
            values.put("name", "add_itcast" + random.nextInt(10));
            Uri newUri = resolver.insert(uri, values);
            Toast.makeText(this, "添加成功", Toast.LENGTH_SHORT).show();
            Log.i("数据库应用", "添加");
        }
        else if (v.getId() == R.id.btn_delete) {
            int deleteCount = resolver.delete(uri, "name=?", new String[]{"itcast0"});
            Toast.makeText(this, "成功删除了" + deleteCount + "行", Toast.LENGTH_SHORT).show();
            Log.i("数据库应用", "删除");
        }
        else if (v.getId() == R.id.btn_select) {
            Cursor cursor = resolver.query(uri, new String[]{"_id", "name"},
                    null, null, null);
            StringBuilder result = new StringBuilder();
            while (cursor.moveToNext()) {
                result.append("ID: ").append(cursor.getString(0))
                        .append(", Name: ").append(cursor.getString(1))
                        .append("\n");
            }
            cursor.close();
            Toast.makeText(this, result.toString(), Toast.LENGTH_LONG).show();
            Log.i("数据库应用", "查询结果");
        }
        else if (v.getId() == R.id.btn_update) {
            values.put("name", "update_itcast");
            int updateCount = resolver.update(uri, values, "name=?",
                    new String[]{"itcast1"});
            Toast.makeText(this, "成功更新了" + updateCount + "行", Toast.LENGTH_SHORT).show();
            Log.i("数据库应用", "更新");
        }
    }
}