package com.example.contentobserverdb1;

import android.content.ContentProvider;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;

public class PersonProvider extends ContentProvider {
    // URI路径匹配器
    private static UriMatcher mUriMatcher = new UriMatcher(UriMatcher.NO_MATCH);
    private static final int SUCCESS = 1; // 匹配成功返回码
    private PersonDBOpenHelper helper;

    // 添加路径匹配规则
    static {
        mUriMatcher.addURI("com.example.contentobserverdb", "info", SUCCESS);
    }

    @Override
    public boolean onCreate() {
        helper = new PersonDBOpenHelper(getContext());
        return true; // 返回true表示初始化成功
    }

    @Override
    public Cursor query(Uri uri, String[] projection, String selection,
                        String[] selectionArgs, String sortOrder) {
        int code = mUriMatcher.match(uri);
        if (code == SUCCESS) {
            SQLiteDatabase db = helper.getReadableDatabase();
            return db.query("info", projection, selection, selectionArgs,
                    null, null, sortOrder);
        } else {
            throw new IllegalArgumentException("路径不正确，无法查询数据！");
        }
    }

    @Override
    public Uri insert(Uri uri, ContentValues values) {
        int code = mUriMatcher.match(uri);
        if (code == SUCCESS) {
            SQLiteDatabase db = helper.getWritableDatabase();
            long rowId = db.insert("info", null, values);
            if (rowId > 0) {
                Uri insertedUri = ContentUris.withAppendedId(uri, rowId);
                // 通知数据变化
                getContext().getContentResolver().notifyChange(insertedUri, null);
                return insertedUri;
            }
            return uri;
        } else {
            throw new IllegalArgumentException("路径不正确，无法插入数据！");
        }
    }

    @Override
    public int delete(Uri uri, String selection, String[] selectionArgs) {
        int code = mUriMatcher.match(uri);
        if (code == SUCCESS) {
            SQLiteDatabase db = helper.getWritableDatabase();
            int count = db.delete("info", selection, selectionArgs);
            // 通知数据变化
            if (count > 0) {
                getContext().getContentResolver().notifyChange(uri, null);
            }
            return count;
        } else {
            throw new IllegalArgumentException("路径不正确，无法删除数据!");
        }
    }

    @Override
    public int update(Uri uri, ContentValues values, String selection,
                      String[] selectionArgs) {
        int code = mUriMatcher.match(uri);
        if (code == SUCCESS) {
            SQLiteDatabase db = helper.getWritableDatabase();
            int count = db.update("info", values, selection, selectionArgs);
            // 通知数据变化
            if (count > 0) {
                getContext().getContentResolver().notifyChange(uri, null);
            }
            return count;
        } else {
            throw new IllegalArgumentException("路径不正确，无法更新数据！");
        }
    }

    @Override
    public String getType(Uri uri) {
        return null;
    }
}